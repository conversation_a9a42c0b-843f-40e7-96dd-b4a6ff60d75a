/**
 * Teste para debugar o problema com o Resend
 */

// Simular a resposta do Resend que pode estar causando o problema
function testResendResponses() {
  console.log('=== Testando diferentes respostas do Resend ===\n');

  // Cenário 1: Resposta normal de sucesso
  console.log('1. Resposta normal de sucesso:');
  const successResponse = {
    data: [{ id: 'msg-123' }],
    error: null
  };
  processResendResponse(successResponse, 1);

  // Cenário 2: Resposta com erro
  console.log('\n2. Resposta com erro:');
  const errorResponse = {
    data: null,
    error: { message: 'Invalid email address' }
  };
  processResendResponse(errorResponse, 1);

  // Cenário 3: Resposta sem dados e sem erro (problema suspeito)
  console.log('\n3. Resposta sem dados e sem erro:');
  const emptyResponse = {
    data: null,
    error: null
  };
  processResendResponse(emptyResponse, 1);

  // Cenário 4: Data não é array
  console.log('\n4. Data não é array:');
  const nonArrayResponse = {
    data: { id: 'msg-123' },
    error: null
  };
  processResendResponse(nonArrayResponse, 1);

  // Cenário 5: Array vazio
  console.log('\n5. Array vazio:');
  const emptyArrayResponse = {
    data: [],
    error: null
  };
  processResendResponse(emptyArrayResponse, 1);
}

function processResendResponse(response, batchSize) {
  const { data, error } = response;
  const allResults = [];
  let totalSent = 0;
  let totalFailed = 0;
  const errors = [];

  console.log('  Resposta recebida:', {
    hasError: !!error,
    hasData: !!data,
    dataType: typeof data,
    isArray: Array.isArray(data),
    dataLength: Array.isArray(data) ? data.length : 'N/A',
    batchSize
  });

  if (error) {
    console.log('  → Processando como erro');
    for (let j = 0; j < batchSize; j++) {
      allResults.push({
        success: false,
        error: error.message || 'Erro no envio em lote'
      });
      totalFailed++;
    }
    errors.push(error.message || 'Erro no envio em lote');
  } else if (data && Array.isArray(data)) {
    console.log('  → Processando como sucesso');
    for (let i = 0; i < batchSize; i++) {
      const emailResult = data[i];
      allResults.push({
        success: true,
        messageId: emailResult?.id,
        metadata: {
          provider: 'resend',
          timestamp: new Date().toISOString()
        }
      });
      totalSent++;
    }
  } else {
    console.log('  → Processando como resposta inesperada (NOVO TRATAMENTO)');
    for (let j = 0; j < batchSize; j++) {
      allResults.push({
        success: false,
        error: 'Resposta inesperada do provedor de email'
      });
      totalFailed++;
    }
    errors.push('Resposta inesperada do provedor de email');
  }

  console.log('  Resultado final:', {
    totalResults: allResults.length,
    totalSent,
    totalFailed,
    errors: errors.length
  });
}

// Executar teste
testResendResponses();

console.log('\n=== Análise ===');
console.log('O problema mais provável é o cenário 3 ou 5:');
console.log('- Cenário 3: Resend retorna { data: null, error: null }');
console.log('- Cenário 5: Resend retorna { data: [], error: null }');
console.log('');
console.log('A correção adicionada trata esses casos como erro,');
console.log('garantindo que sempre haja um resultado para cada email enviado.');
