/**
 * Implementação do provedor Resend para envio de e-mails
 * Utiliza a API do Resend para envio de e-mails transacionais
 */

import { Resend } from 'resend';
import {
  EmailProvider,
  EmailProviderBase,
  EmailData,
  EmailResult,
  BatchEmailResult,
  EmailDeliveryStatus,
  DomainVerification
} from './email-provider';

export interface ResendConfig {
  apiKey: string;
  fromDomain: string;
  webhookSecret?: string;
}

export class ResendProvider extends EmailProviderBase implements EmailProvider {
  private resend: Resend;
  private config: ResendConfig;

  constructor(config: ResendConfig) {
    super('resend');
    this.config = config;
    this.resend = new Resend(config.apiKey);
  }

  /**
   * Envia um e-mail através do Resend
   */
  async send(email: EmailData): Promise<EmailResult> {
    try {

      // Validar dados do e-mail
      const validation = this.validateEmailData(email);
      if (!validation.isValid) {
        console.error('🔍 [DEBUG] Validação falhou:', validation.errors);
        return {
          success: false,
          error: `Dados inválidos: ${validation.errors.join(', ')}`
        };
      }

      // Preparar dados para o Resend
      const resendData = {
        from: email.fromName ? `${email.fromName} <${email.from}>` : email.from,
        to: Array.isArray(email.to) ? email.to : [email.to],
        subject: email.subject,
        html: email.html,
        text: email.text,
        reply_to: email.replyTo,
        cc: email.cc ? (Array.isArray(email.cc) ? email.cc : [email.cc]) : undefined,
        bcc: email.bcc ? (Array.isArray(email.bcc) ? email.bcc : [email.bcc]) : undefined,
        attachments: email.attachments?.map(att => ({
          filename: att.filename,
          content: att.content,
          content_type: att.contentType,
          cid: att.cid
        })),
        headers: email.headers,
        tags: email.tags?.map(tag => ({ name: 'category', value: tag }))
      };


      const { data, error } = await this.resend.emails.send(resendData);

      if (error) {
        console.error('🔍 [DEBUG] Erro do Resend:', error);
        console.error('🔍 [DEBUG] Tipo do erro:', typeof error);
        console.error('🔍 [DEBUG] Erro completo:', JSON.stringify(error, null, 2));
        return {
          success: false,
          error: error.message || 'Erro desconhecido do Resend'
        };
      }

      return {
        success: true,
        messageId: data?.id,
        metadata: {
          provider: 'resend',
          timestamp: new Date().toISOString(),
          ...email.metadata
        }
      };

    } catch (error) {
      console.error('🔍 [DEBUG] Erro capturado no catch:', error);
      console.error('🔍 [DEBUG] Stack trace:', error instanceof Error ? error.stack : 'N/A');
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Envia múltiplos e-mails em lote usando a API batch nativa do Resend
   */
  async sendBatch(emails: EmailData[]): Promise<BatchEmailResult> {
    if (emails.length === 0) {
      return {
        success: true,
        results: [],
        totalSent: 0,
        totalFailed: 0,
        errors: []
      };
    }

    try {
      // Validar todos os emails antes de enviar
      const validationErrors: string[] = [];
      for (let i = 0; i < emails.length; i++) {
        const validation = this.validateEmailData(emails[i]);
        if (!validation.isValid) {
          validationErrors.push(`Email ${i + 1}: ${validation.errors.join(', ')}`);
        }
      }

      if (validationErrors.length > 0) {
        return {
          success: false,
          results: emails.map(() => ({ success: false, error: 'Dados inválidos' })),
          totalSent: 0,
          totalFailed: emails.length,
          errors: validationErrors
        };
      }

      // Resend suporta até 100 emails por lote
      const BATCH_SIZE = 100;
      const batches: EmailData[][] = [];

      for (let i = 0; i < emails.length; i += BATCH_SIZE) {
        batches.push(emails.slice(i, i + BATCH_SIZE));
      }

      const allResults: EmailResult[] = [];
      let totalSent = 0;
      let totalFailed = 0;
      const errors: string[] = [];

      // Processar cada lote
      for (const batch of batches) {
        try {
          // Converter para formato do Resend batch
          const batchEmails = batch.map(email => ({
            from: email.fromName ? `${email.fromName} <${email.from}>` : email.from,
            to: Array.isArray(email.to) ? email.to : [email.to],
            subject: email.subject,
            html: email.html,
            text: email.text,
            reply_to: email.replyTo,
            cc: email.cc ? (Array.isArray(email.cc) ? email.cc : [email.cc]) : undefined,
            bcc: email.bcc ? (Array.isArray(email.bcc) ? email.bcc : [email.bcc]) : undefined,
            attachments: email.attachments?.map(att => ({
              filename: att.filename,
              content: att.content,
              content_type: att.contentType,
              cid: att.cid
            })),
            headers: email.headers,
            tags: email.tags?.map(tag => ({ name: 'category', value: tag }))
          }));

          // Usar a API batch nativa do Resend
          const { data, error } = await this.resend.batch.send(batchEmails);

          console.log('🔍 [DEBUG] Resend batch response:', {
            hasError: !!error,
            hasData: !!data,
            dataType: typeof data,
            isArray: Array.isArray(data),
            dataLength: Array.isArray(data) ? data.length : 'N/A',
            batchSize: batch.length
          });

          if (error) {
            console.error('🔍 [DEBUG] Erro do Resend batch:', error);
            // Se falhar o lote inteiro, marcar todos como falha
            for (let j = 0; j < batch.length; j++) {
              allResults.push({
                success: false,
                error: error.message || 'Erro no envio em lote'
              });
              totalFailed++;
            }
            errors.push(error.message || 'Erro no envio em lote');
          } else if (data && Array.isArray(data)) {
            console.log('🔍 [DEBUG] Processando dados do Resend:', data);
            // Sucesso no lote
            for (let i = 0; i < batch.length; i++) {
              const emailResult = data[i];
              allResults.push({
                success: true,
                messageId: emailResult?.id,
                metadata: {
                  provider: 'resend',
                  timestamp: new Date().toISOString(),
                  batchId: `batch-${Date.now()}`,
                  ...batch[i].metadata
                }
              });
              totalSent++;
            }
          } else {
            // Caso onde não há erro mas também não há dados válidos
            console.error('🔍 [DEBUG] Resposta inesperada do Resend - sem erro mas sem dados válidos:', { data, error });
            for (let j = 0; j < batch.length; j++) {
              allResults.push({
                success: false,
                error: 'Resposta inesperada do provedor de email'
              });
              totalFailed++;
            }
            errors.push('Resposta inesperada do provedor de email');
          }

        } catch (batchError) {
          console.error('🔍 [DEBUG] Erro ao processar lote:', batchError);
          // Se der erro no lote, marcar todos como falha
          for (let k = 0; k < batch.length; k++) {
            allResults.push({
              success: false,
              error: batchError instanceof Error ? batchError.message : 'Erro desconhecido no lote'
            });
            totalFailed++;
          }
          errors.push(batchError instanceof Error ? batchError.message : 'Erro desconhecido no lote');
        }
      }

      return {
        success: totalFailed === 0,
        results: allResults,
        totalSent,
        totalFailed,
        errors
      };

    } catch (error) {
      console.error('🔍 [DEBUG] Erro geral no sendBatch:', error);
      return {
        success: false,
        results: emails.map(() => ({
          success: false,
          error: error instanceof Error ? error.message : 'Erro desconhecido'
        })),
        totalSent: 0,
        totalFailed: emails.length,
        errors: [error instanceof Error ? error.message : 'Erro desconhecido']
      };
    }
  }

  /**
   * Verifica o status de entrega de um e-mail
   */
  async getDeliveryStatus(messageId: string): Promise<EmailDeliveryStatus> {
    try {
      // Resend não tem API pública para status de entrega ainda
      // Por enquanto, retornamos status básico
      return {
        messageId,
        status: 'sent',
        timestamp: new Date().toISOString(),
        recipient: 'unknown',
        events: []
      };

    } catch (error) {
      console.error('Erro ao verificar status de entrega:', error);
      return {
        messageId,
        status: 'failed',
        timestamp: new Date().toISOString(),
        recipient: 'unknown',
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Verifica se um domínio está configurado corretamente
   */
  async verifyDomain(domain: string): Promise<DomainVerification> {
    try {
      // Resend tem API para verificação de domínio
      const { data, error } = await this.resend.domains.get(domain);

      if (error) {
        return {
          domain,
          verified: false,
          error: error.message
        };
      }

      return {
        domain,
        verified: data?.status === 'verified',
        dnsRecords: data?.records?.map((record: any) => ({
          type: record.type,
          name: record.name,
          value: record.value,
          status: record.status as 'verified' | 'pending' | 'failed'
        }))
      };

    } catch (error) {
      console.error('Erro ao verificar domínio:', error);
      return {
        domain,
        verified: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Valida se o provedor está configurado corretamente
   */
  async validateConfiguration(): Promise<boolean> {
    try {
      // Testa a configuração fazendo uma chamada simples à API
      const { error } = await this.resend.domains.list();
      return !error;

    } catch (error) {
      console.error('Erro na validação da configuração Resend:', error);
      return false;
    }
  }

  /**
   * Retorna a configuração atual (sem dados sensíveis)
   */
  getConfig(): Omit<ResendConfig, 'apiKey' | 'webhookSecret'> {
    return {
      fromDomain: this.config.fromDomain
    };
  }
}
